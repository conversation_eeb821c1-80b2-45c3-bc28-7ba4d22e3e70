import { Injectable } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService {
  private breadcrumbsSubject = new BehaviorSubject<BreadcrumbItem[]>([]);
  public breadcrumbs$: Observable<BreadcrumbItem[]> = this.breadcrumbsSubject.asObservable();

  // Route to breadcrumb mapping for top 3 menus
  private routeMap: { [key: string]: BreadcrumbItem[] } = {
    // Dashboard Routes
    '/home/<USER>': [
      { label: 'Dashboard', icon: 'dashboard' },
      { label: 'Welcome' }
    ],
    '/home/<USER>/reporting': [
      { label: 'Dashboard', icon: 'dashboard' },
      { label: 'Reporting Dashboard' }
    ],

    // Reports Routes - Sales Reports
    '/home/<USER>/sales/salesreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Sales Reports' },
      { label: 'Sales Report' }
    ],
    '/home/<USER>/sales/salesordertrails': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Sales Reports' },
      { label: 'Sales Order Trails' }
    ],

    // Reports Routes - Stock Reports
    '/home/<USER>/stock/productwisestockwithsupplier': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Stock Availability' }
    ],
    '/home/<USER>/stock/categorywisestock': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Category Wise Stock' }
    ],
    '/home/<USER>/stock/stockconsumptionreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Stock Consumption' }
    ],
    '/home/<USER>/stock/stockreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Stock' }
    ],
    '/home/<USER>/stock/storewisestock': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Store Wise Stock' }
    ],
    '/home/<USER>/stock/productwiseStock': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Product Wise Stock' }
    ],
    '/home/<USER>/stock/productstockhistory': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Materials Stock Reports' },
      { label: 'Product Stock History' }
    ],
    '/home/<USER>/stock/productstocksummaryreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Product Stock Reports' },
      { label: 'Product Stock Summary' }
    ],

    // Reports Routes - Purchase Reports
    '/home/<USER>/purchase/purchasereport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Purchase Reports' },
      { label: 'Purchase' }
    ],
    '/home/<USER>/purchase/supplierproductmapping': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Purchase Reports' },
      { label: 'Supplier Product Mapping' }
    ],

    // Reports Routes - Production Reports
    '/home/<USER>/production/productionplanningreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Production Planning' }
    ],
    '/home/<USER>/production/productionstatusreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Production Status' }
    ],
    '/home/<USER>/production/productionheartbeat': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Production Heartbeat' }
    ],
    '/home/<USER>/production/pasteconsumptionreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Paste Consumption' }
    ],
    '/home/<USER>/production/postprocessreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Post Process Report' }
    ],
    '/home/<USER>/production/yieldreport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Yield' }
    ],
    '/home/<USER>/production/wastagereport': [
      { label: 'Reports', icon: 'bar-chart' },
      { label: 'Production Reports' },
      { label: 'Wastage Report' }
    ],

    // Sales Routes
    '/home/<USER>/add': [
      { label: 'Sales', icon: 'fund' },
      { label: 'Sales Order' },
      { label: 'Add' }
    ],
    '/home/<USER>/list': [
      { label: 'Sales', icon: 'fund' },
      { label: 'Sales Order' },
      { label: 'List' }
    ],
    '/home/<USER>/proformaInvoice/list': [
      { label: 'Sales', icon: 'fund' },
      { label: 'Proforma Invoice' },
      { label: 'List' }
    ],
    '/home/<USER>/proformaInvoice/add': [
      { label: 'Sales', icon: 'fund' },
      { label: 'Proforma Invoice' },
      { label: 'Add' }
    ]
  };

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        map(() => this.router.url)
      )
      .subscribe(url => {
        this.updateBreadcrumbs(url);
      });
  }

  private updateBreadcrumbs(url: string): void {
    // Remove query parameters and fragments for matching
    const cleanUrl = url.split('?')[0].split('#')[0];
    
    // Check for exact match first
    if (this.routeMap[cleanUrl]) {
      this.breadcrumbsSubject.next(this.routeMap[cleanUrl]);
      return;
    }

    // Check for dynamic routes (with parameters)
    const matchedRoute = this.findMatchingRoute(cleanUrl);
    if (matchedRoute) {
      this.breadcrumbsSubject.next(matchedRoute);
      return;
    }

    // Default empty breadcrumbs for unmapped routes
    this.breadcrumbsSubject.next([]);
  }

  private findMatchingRoute(url: string): BreadcrumbItem[] | null {
    // Handle dynamic routes like /home/<USER>/sales/salesordertrails/:id
    for (const route in this.routeMap) {
      if (this.isRouteMatch(route, url)) {
        return this.routeMap[route];
      }
    }
    return null;
  }

  private isRouteMatch(routePattern: string, actualUrl: string): boolean {
    // Simple pattern matching for routes with parameters
    const patternParts = routePattern.split('/');
    const urlParts = actualUrl.split('/');

    if (patternParts.length !== urlParts.length) {
      return false;
    }

    for (let i = 0; i < patternParts.length; i++) {
      if (patternParts[i].startsWith(':')) {
        // This is a parameter, skip comparison
        continue;
      }
      if (patternParts[i] !== urlParts[i]) {
        return false;
      }
    }

    return true;
  }

  // Method to manually set breadcrumbs if needed
  setBreadcrumbs(breadcrumbs: BreadcrumbItem[]): void {
    this.breadcrumbsSubject.next(breadcrumbs);
  }

  // Method to get current breadcrumbs synchronously
  getCurrentBreadcrumbs(): BreadcrumbItem[] {
    return this.breadcrumbsSubject.value;
  }
}
