/**
 * Extended Route Configuration - Continuation of menu structures
 * This file contains the remaining menu structures for the PMS application
 */

import { MenuStructure, ROUTES } from './route-config';

export const EXTENDED_MENU_STRUCTURE: MenuStructure[] = [
  {
    id: 'dispatch',
    label: 'Dispatch',
    icon: 'car',
    permission: { module: 'Dispatch' },
    openMapKey: 'sub7',
    dataLabel: 'Dispatch',
    children: [
      {
        path: '',
        label: 'Packaging',
        icon: 'dropbox',
        permission: { module: 'Dispatch - Packaging' },
        children: [
          {
            path: ROUTES.DISPATCH.PACKAGING.LIST,
            label: 'List',
            permission: { module: 'Dispatch - Packaging', responsibility: 'View' }
          },
          {
            path: ROUTES.DISPATCH.PACKAGING.ADD,
            label: 'Add',
            permission: { module: 'Dispatch - Packaging', responsibility: 'Add' }
          }
        ]
      }
    ]
  },
  {
    id: 'issue',
    label: 'Issue',
    icon: 'inbox',
    permission: { module: 'Issue' },
    openMapKey: 'sub8',
    dataLabel: 'Issue',
    children: [
      {
        path: ROUTES.ISSUE.LIST,
        label: 'List',
        permission: { module: 'Issue', responsibility: 'View' }
      },
      {
        path: ROUTES.ISSUE.ADD,
        label: 'Add',
        permission: { module: 'Issue', responsibility: 'Add' }
      }
    ]
  },
  {
    id: 'consumption',
    label: 'Consumption',
    icon: 'minus-circle',
    permission: { module: 'Consumption' },
    openMapKey: 'sub9',
    dataLabel: 'Consumption',
    children: [
      {
        path: ROUTES.CONSUMPTION.LIST,
        label: 'List',
        permission: { module: 'Consumption', responsibility: 'View' }
      },
      {
        path: ROUTES.CONSUMPTION.PENDING_ORDERS,
        label: 'Pending Orders List',
        permission: { module: 'Consumption', responsibility: 'View' }
      },
      {
        path: ROUTES.CONSUMPTION.ADD,
        label: 'Add',
        permission: { module: 'Consumption', responsibility: 'Add' }
      }
    ]
  },
  {
    id: 'costing',
    label: 'Costing',
    icon: 'dollar',
    permission: { module: 'Costing' },
    openMapKey: 'sub10',
    dataLabel: 'Costing',
    children: [
      {
        path: ROUTES.COSTING.LIST,
        label: 'List',
        permission: { module: 'Costing', responsibility: 'View' }
      },
      {
        path: '',
        label: 'Estimation',
        icon: 'calculator',
        permission: { module: 'Costing - Estimation', responsibility: 'View' },
        children: [
          {
            path: ROUTES.COSTING.ESTIMATION.ADD,
            label: 'Add',
            permission: { module: 'Costing - Estimation', responsibility: 'Add' }
          },
          {
            path: ROUTES.COSTING.ESTIMATION.LIST,
            label: 'List',
            permission: { module: 'Costing - Estimation', responsibility: 'View' }
          }
        ]
      },
      {
        path: ROUTES.COSTING.OVERHEAD,
        label: 'Overhead',
        permission: { module: 'Costing - Overhead', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: 'database',
    permission: { module: 'Inventory' },
    openMapKey: 'sub11',
    dataLabel: 'Inventory',
    children: [
      {
        path: ROUTES.INVENTORY.OPENING_STOCK,
        label: 'Opening Stock',
        permission: { module: 'Inventory - Opening Stock' }
      },
      {
        path: ROUTES.INVENTORY.ADD_STOCK,
        label: 'Add Stock',
        permission: { module: 'Inventory - Add Stock' }
      },
      {
        path: ROUTES.INVENTORY.KNITTING_DIVISION_STOCK,
        label: 'Knitting Division Stock List',
        permission: { module: 'Inventory - Knitting Division Stock' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_LIST,
        label: 'Stock Listing',
        permission: { module: 'Inventory - Stock List' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_LABEL_LIST,
        label: 'Stock Label Listing',
        permission: { module: 'Inventory - Stock Label List' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_QUALITY_INSPECTION,
        label: 'Stock Quality Inspection',
        permission: { module: 'Inventory - Stock Quality Inspection' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_INSPECTION,
        label: 'Stock Inspection',
        permission: { module: 'Inventory - Inspection' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_ALLOCATION,
        label: 'Stock Allocation',
        permission: { module: 'Inventory - Allocation' }
      },
      {
        path: ROUTES.INVENTORY.STOCK_REJECTED,
        label: 'Stock Rejected Items',
        permission: { module: 'Inventory - Rejected' }
      }
    ]
  },
  {
    id: 'gate',
    label: 'Gate In/Out',
    icon: 'swap',
    permission: { module: 'Gate' },
    openMapKey: 'sub12',
    dataLabel: 'Gate',
    children: [
      {
        path: ROUTES.GATE.GATE_IN,
        label: 'Gate In',
        permission: { module: 'Gate In' }
      },
      {
        path: ROUTES.GATE.GATE_OUT,
        label: 'Gate Out',
        permission: { module: 'Gate Out' }
      }
    ]
  },
  {
    id: 'gate-pass',
    label: 'Issue Gate Pass',
    icon: 'export',
    permission: { module: 'Issue Gate Pass' },
    openMapKey: 'sub13',
    dataLabel: 'Gate Pass',
    children: [
      {
        path: ROUTES.GATE_PASS.ISSUE,
        label: 'Issue Gate Pass',
        permission: { module: 'Issue Gate Pass' }
      }
    ]
  },
  {
    id: 'out-pass',
    label: 'Out pass',
    icon: 'poweroff',
    permission: { module: 'Out Pass' },
    openMapKey: 'sub14',
    dataLabel: 'Out Pass',
    children: [
      {
        path: ROUTES.OUT_PASS.ADD,
        label: 'Add',
        permission: { module: 'Out Pass', responsibility: 'Add' }
      },
      {
        path: ROUTES.OUT_PASS.LIST,
        label: 'List',
        permission: { module: 'Out Pass', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'quick-tools',
    label: 'Quick Tools',
    icon: 'tool',
    permission: { module: 'QuickTools' },
    openMapKey: 'sub15',
    dataLabel: 'Tools',
    children: [
      {
        path: ROUTES.QUICK_TOOLS.MEASUREMENT_CONVERSION,
        label: 'Measurement Conversion',
        permission: { module: 'QuickTools - Measurement Conversion', responsibility: 'View' }
      },
      {
        path: ROUTES.QUICK_TOOLS.BARCODE_SCANNER,
        label: 'Barcode Scanner',
        permission: { module: 'QuickTools - Barcode Scanner', responsibility: 'View' }
      }
    ]
  }
];

// Helper function to combine all menu structures
export function getAllMenuStructures(): MenuStructure[] {
  // This will be imported and combined in the main route-config file
  return EXTENDED_MENU_STRUCTURE;
}
