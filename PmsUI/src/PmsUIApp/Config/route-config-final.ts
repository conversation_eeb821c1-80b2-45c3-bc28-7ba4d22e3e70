/**
 * Final Route Configuration - Master, Admin, and Notification menus
 * This file contains the final menu structures for the PMS application
 */

import { MenuStructure, ROUTES } from './route-config';

export const FINAL_MENU_STRUCTURE: MenuStructure[] = [
  {
    id: 'master',
    label: 'Master',
    icon: 'setting',
    permission: { module: 'Master' },
    openMapKey: 'sub17',
    dataLabel: 'Master',
    children: [
      {
        path: '',
        label: 'General',
        icon: 'setting',
        permission: { module: 'Master' },
        children: [
          {
            path: ROUTES.MASTER.GENERAL.BRANCH,
            label: 'Branch',
            permission: { module: 'Master - Branch', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.BANK_DETAILS,
            label: 'Bank Details',
            permission: { module: 'Master - BankDetails', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.DEPARTMENT,
            label: 'Department',
            permission: { module: 'Master - Department', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.STORE,
            label: 'Store',
            permission: { module: 'Master - Store', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.RACK,
            label: 'Rack',
            permission: { module: 'Master - Rack', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.TAG,
            label: 'Tag',
            permission: { module: 'Master - Tag', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.PAYMENT_TERM,
            label: 'Payment Term',
            permission: { module: 'Master - PaymentTerm', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.DELIVERY_TERM,
            label: 'Delivery Term',
            permission: { module: 'Master - DeliveryTerm', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.TRANSPORT,
            label: 'Transport',
            permission: { module: 'Master - Transport', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.GENERAL.MEASUREMENT_UNIT,
            label: 'Measurement Unit',
            permission: { module: 'Master - MeasurementUnit', responsibility: 'View' }
          }
        ]
      },
      {
        path: '',
        label: 'Product',
        icon: 'appstore',
        permission: { module: 'Master - Product' },
        children: [
          {
            path: ROUTES.MASTER.PRODUCT.PRODUCT_LIST,
            label: 'Product List',
            permission: { module: 'Master - Product', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCT.PRODUCT_CATEGORY,
            label: 'Product Category',
            permission: { module: 'Master - ProductCategory', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCT.PRODUCT_FIRST_SUB_CATEGORY,
            label: 'Product First Sub Category',
            permission: { module: 'Master - ProductFirstSubCategory', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCT.PRODUCT_SEC_SUB_CATEGORY,
            label: 'Product Sec Sub Category',
            permission: { module: 'Master - ProductSecSubCategory', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.PRODUCT.PRODUCT_TRANSFER,
            label: 'Product Transfer',
            permission: { module: 'Master - ProductTransfer', responsibility: 'View' }
          }
        ]
      },
      {
        path: '',
        label: 'Supplier',
        icon: 'team',
        permission: { module: 'Master - Supplier' },
        children: [
          {
            path: ROUTES.MASTER.SUPPLIER.SUPPLIER_LIST,
            label: 'Supplier List',
            permission: { module: 'Master - Supplier', responsibility: 'View' }
          }
        ]
      },
      {
        path: '',
        label: 'Attributes',
        icon: 'tags',
        permission: { module: 'Master - Attributes' },
        children: [
          {
            path: ROUTES.MASTER.ATTRIBUTES.COLOR,
            label: 'Color',
            permission: { module: 'Master - Color', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.ATTRIBUTES.ELEMENT,
            label: 'Element',
            permission: { module: 'Master - Element', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.ATTRIBUTES.GRAIN,
            label: 'Grain',
            permission: { module: 'Master - Grain', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.ATTRIBUTES.THICKNESS,
            label: 'Thickness',
            permission: { module: 'Master - Thickness', responsibility: 'View' }
          },
          {
            path: ROUTES.MASTER.ATTRIBUTES.WIDTH,
            label: 'Width',
            permission: { module: 'Master - Width', responsibility: 'View' }
          }
        ]
      }
    ]
  },
  {
    id: 'admin',
    label: 'Admin',
    icon: 'user',
    permission: { module: 'Admin' },
    openMapKey: 'sub18',
    dataLabel: 'Admin',
    children: [
      {
        path: ROUTES.ADMIN.USERS,
        label: 'Manage Users',
        permission: { module: 'Admin - Users', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.OPERATIONS,
        label: 'Manage Operations',
        permission: { module: 'Admin - Operations', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.ROLE_MASTER,
        label: 'Manage Roles',
        permission: { module: 'Admin - RoleMaster', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.GENERAL_CONFIG,
        label: 'General Configuration',
        permission: { module: 'Admin - GeneralConfig', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.USERS_LOGIN_HISTORY,
        label: 'Users Login History',
        permission: { module: 'Admin - UsersLoginHistory', responsibility: 'View' }
      },
      {
        path: ROUTES.ADMIN.VALIDATION_RULES,
        label: 'Validation Rules Config',
        permission: { module: 'Admin - ValidationRules', responsibility: 'View' }
      }
    ]
  },
  {
    id: 'notification',
    label: 'Notification',
    icon: 'bell',
    permission: { module: 'Notification' },
    openMapKey: 'sub19',
    dataLabel: 'Notification',
    children: [
      {
        path: ROUTES.NOTIFICATION.LIST,
        label: 'List',
        permission: { module: 'Notification', responsibility: 'View' }
      },
      {
        path: ROUTES.NOTIFICATION.ADD,
        label: 'Add',
        permission: { module: 'Notification', responsibility: 'Add' }
      }
    ]
  }
];

// Helper function to get final menu structures
export function getFinalMenuStructures(): MenuStructure[] {
  return FINAL_MENU_STRUCTURE;
}
