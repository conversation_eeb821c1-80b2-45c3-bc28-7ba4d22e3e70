:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
}

.menu-sidebar {
  position: fixed;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
  height: 100vh;
  left: 0;
}

.sidebar-overflow {
  overflow-y: scroll;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}

.noncollapsed-content {
  margin-left: 96px;
}

.collapsed-content {
  margin-left: 273px;
}

.header-trigger {
  height: 124px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all .3s, padding 0s;
}

.noncollapse-trigger {
  margin-left: 71px;
}

.collapse-trigger {
  margin-left: 248px;
}

.trigger:hover {
  color: #1890ff;
}

.sidebar-logo {
  position: relative;
  height: 70px;
  padding-left: 24px;
  overflow: hidden;
  line-height: 65px;
  border-bottom: 1px solid white;
  background: #001529;
  transition: all .3s;
  margin-bottom: 5px;
}

.sidebar-logo_userfull {
  display: inline-block;
  height: 42px;
  width: 42px;
  vertical-align: middle;
  border-radius: 50%;
}

.sidebar-logo_usericon {
  display: inline-block;
  height: 25px;
  width: 25px;
  vertical-align: middle;
  border-radius: 50%;
}

.sidebar-logo h1 {
  display: inline-block;
  margin: 0 0 0 20px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  vertical-align: middle;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
}

nz-content {
  margin: 24px;
}

.inner-content {
  padding: 24px;
  background: #fff;
  height: 100%;
}

.logout {
  display: inline-block;
  float: right;
  margin-right: 25px;
}

.logout span {
  margin-right: 15px;
}

.sideMenuHeading {
  color: #fff;
  font-weight: 600;
  border-bottom: 1px solid #fff;
}

/* Collapsed menu labels using pseudo-elements */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item::after {
  content: attr(data-label);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 6px;
  font-size: 9px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  pointer-events: none;
  z-index: 999;
  max-width: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  width: auto;
}

/* Adjust submenu item positioning when collapsed */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu {
  position: relative !important;
  height: 60px !important;
  overflow: visible !important;
  width: 80px !important;
}

/* Ensure proper spacing for collapsed menu items */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title {
  height: 60px !important;
  line-height: 60px !important;
  position: relative !important;
  padding: 0 !important;
  text-align: center !important;
  width: 80px !important;
  display: block !important;
}

/* Position the icon properly without breaking layout */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title .anticon {
  font-size: 16px !important;
  margin: 0 !important;
  display: block !important;
  text-align: center !important;
  width: 100% !important;
  line-height: 32px !important;
  margin-top: 8px !important;
}

/* Hover effect for collapsed menu labels */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item:hover::after {
  color: #1890ff !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .collapsed-menu-label {
    font-size: 9px;
    bottom: 2px;
    max-width: 50px;
  }
}

/* Breadcrumb Styling */
.breadcrumb-container {
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  padding: 8px 24px;
  margin-bottom: 0;
  margin-top: -16px;
  /* Reduce space from nz-content */
}

/* Adjust nz-content padding to accommodate breadcrumb */
::ng-deep nz-content {
  padding-top: 0 !important;
}

/* Ensure the content after breadcrumb has proper spacing */
.breadcrumb-container+div {
  padding-top: 16px;
}

.breadcrumb-nav {
  margin: 0;
  font-size: 14px;
}

.breadcrumb-nav .ant-breadcrumb-link {
  color: #595959;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb-nav .ant-breadcrumb-link:hover {
  color: #1890ff;
}

.breadcrumb-nav .ant-breadcrumb-link i {
  margin-right: 4px;
}

.breadcrumb-nav .ant-breadcrumb-separator {
  color: #bfbfbf;
  margin: 0 8px;
}

.breadcrumb-nav .ant-breadcrumb-link:last-child,
.breadcrumb-nav .ant-breadcrumb-link:last-child:hover {
  color: #262626;
  font-weight: 500;
}

/* Responsive breadcrumb styling */
@media (max-width: 768px) {
  .breadcrumb-container {
    padding: 8px 16px;
  }

  .breadcrumb-nav {
    font-size: 12px;
  }

  .breadcrumb-nav .ant-breadcrumb-separator {
    margin: 0 4px;
  }
}

/* Enhanced Menu Selection Styling */

/* Main menu item selected state - expanded sidebar */
::ng-deep .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title,
::ng-deep .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title {
  background-color: #1890ff !important;
  color: #fff !important;
  border-radius: 4px !important;
  margin: 2px 8px !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

/* Main menu item selected state - collapsed sidebar */
::ng-deep .ant-menu-inline-collapsed .ant-menu-submenu-selected>.ant-menu-submenu-title,
::ng-deep .ant-menu-inline-collapsed .ant-menu-submenu-active>.ant-menu-submenu-title {
  background-color: #1890ff !important;
  color: #fff !important;
  border-radius: 6px !important;
  margin: 8px 6px 4px 6px !important;
  width: 68px !important;
  height: 48px !important;
  line-height: 48px !important;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  top: 2px !important;
}

/* Enhanced hover state for main menu items */
::ng-deep .ant-menu-dark .ant-menu-submenu:hover>.ant-menu-submenu-title {
  background-color: rgba(24, 144, 255, 0.8) !important;
  color: #fff !important;
  border-radius: 4px !important;
  margin: 2px 8px !important;
  transition: all 0.3s ease !important;
}

/* Enhanced hover state for collapsed main menu items */
::ng-deep .ant-menu-inline-collapsed .ant-menu-submenu:hover>.ant-menu-submenu-title {
  background-color: rgba(24, 144, 255, 0.8) !important;
  color: #fff !important;
  border-radius: 6px !important;
  margin: 8px 6px 4px 6px !important;
  width: 68px !important;
  height: 28px !important;
  line-height: 48px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  top: 2px !important;
}

/* Icon styling for selected main menu items */
::ng-deep .ant-menu-submenu-selected>.ant-menu-submenu-title .anticon,
::ng-deep .ant-menu-submenu-active>.ant-menu-submenu-title .anticon {
  color: #fff !important;
  font-size: 16px !important;
}

/* Icon styling for hovered main menu items */
::ng-deep .ant-menu-submenu:hover>.ant-menu-submenu-title .anticon {
  color: #fff !important;
  transform: scale(1.1) !important;
  transition: all 0.3s ease !important;
}

/* Sub-menu item enhanced styling */
::ng-deep .ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  border-radius: 4px !important;
  margin: 1px 8px !important;
  color: #fff !important;
  font-weight: 500 !important;
}

/* Sub-menu item hover styling */
::ng-deep .ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.7) !important;
  border-radius: 4px !important;
  margin: 1px 8px !important;
  color: #fff !important;
  transition: all 0.3s ease !important;
}

/* Active link styling within menu items */
::ng-deep .ant-menu-dark .ant-menu-item a.router-link-active,
::ng-deep .ant-menu-dark .ant-menu-item a:hover {
  color: #fff !important;
  text-decoration: none !important;
}

/* Collapsed menu label styling for selected items */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item.ant-menu-submenu-selected::after,
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item.ant-menu-submenu-active::after {
  color: #fff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Collapsed menu label hover styling */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item:hover::after {
  color: #fff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Focus states for accessibility */
::ng-deep .ant-menu-submenu-title:focus,
::ng-deep .ant-menu-item:focus {
  outline: 2px solid #40a9ff !important;
  outline-offset: 2px !important;
}

/* Animation for smooth transitions */
::ng-deep .ant-menu-submenu-title,
::ng-deep .ant-menu-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Ensure proper spacing and alignment */
::ng-deep .ant-menu-dark .ant-menu-submenu-title {
  padding: 0 16px !important;
  height: 40px !important;
  line-height: 40px !important;
  margin: 2px 0 !important;
}

/* Collapsed menu item positioning adjustments */
::ng-deep .ant-menu-inline-collapsed .ant-menu-submenu-title {
  padding: 0 !important;
  text-align: center !important;
  position: relative !important;
}